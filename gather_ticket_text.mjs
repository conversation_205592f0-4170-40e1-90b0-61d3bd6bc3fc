import fs from 'fs';

function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  return date.toISOString().replace('T', ' ').replace('Z', '');
}

function getSenderName(comment) {
  // For chat transcripts (author_id: -1), the sender info is already in the body
  if (comment.author_id === -1) {
    return 'Chat Transcript';
  }
  
  // Try to get sender from via.source.from.name (email comments)
  if (comment.via && comment.via.source && comment.via.source.from && comment.via.source.from.name) {
    return comment.via.source.from.name;
  }
  
  // For other comments, use channel info or author_id
  if (comment.via && comment.via.channel) {
    switch (comment.via.channel) {
      case 'system':
        return 'System';
      case 'api':
        return 'API';
      case 'web':
        return 'Web User';
      case 'sms':
        return 'SMS User';
      case 'native_messaging':
        return 'Web User';
      case 'business_messaging_slack_connect':
        return 'Slack User';
      default:
        return `${comment.via.channel} (${comment.author_id})`;
    }
  }
  
  // Fallback to author_id
  return `User ${comment.author_id}`;
}

function getCommentBody(comment) {
  // Use plain_body if available, otherwise body
  return comment.plain_body || comment.body || '[No content]';
}

function formatCommentsToText() {
  // Read comments from comments.json
  let commentsData;
  try {
    const commentsJson = fs.readFileSync('comments.json', 'utf8');
    commentsData = JSON.parse(commentsJson);
  } catch (error) {
    console.error('Error reading comments.json:', error);
    return;
  }
  
  console.log('Formatting comments to text...');
  
  let output = '';
  const ticketIds = Object.keys(commentsData).sort((a, b) => parseInt(a) - parseInt(b));
  
  for (const ticketId of ticketIds) {
    const comments = commentsData[ticketId];
    
    if (!comments || comments.length === 0) {
      continue;
    }
    
    // Add ticket header
    output += `${ticketId}\n`;
    
    // Sort comments by creation time
    const sortedComments = comments.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    
    // Add each comment
    for (const comment of sortedComments) {
      const timestamp = formatTimestamp(comment.created_at);
      const sender = getSenderName(comment);
      const body = getCommentBody(comment);
      
      // Clean up the body text (remove extra newlines and normalize)
      const cleanBody = body.replace(/\n+/g, ' ').trim();
      
      output += `${timestamp} ${sender}: ${cleanBody}\n`;
    }
    
    // Add blank line between tickets
    output += '\n\n';
  }
  
  return output;
}

function main() {
  console.log('Starting to format comments...');
  
  const formattedText = formatCommentsToText();
  
  if (formattedText) {
    try {
      fs.writeFileSync('ticket_text.txt', formattedText);
      console.log('Formatted comments saved to ticket_text.txt');
      
      // Show some stats
      const lines = formattedText.split('\n').length;
      const tickets = formattedText.split('\n\n\n').length - 1; // -1 because of trailing newlines
      console.log(`Generated ${lines} lines for ${tickets} tickets`);
      
    } catch (error) {
      console.error('Error saving formatted text to file:', error);
    }
  }
}

// Run the script
main();
