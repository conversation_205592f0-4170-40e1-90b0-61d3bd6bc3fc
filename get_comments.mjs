import fs from 'fs';

const ZENDESK_USERNAME = '<EMAIL>/token';
const ZENDESK_TOKEN = 'pilgY6Bm03S3wPcrvG91UeiXKEUfxCLUGzmse6fm';
const ZENDESK_API_URL = 'https://d3v-anastasi-bakolias.zendesk.com/api/v2';

async function getCommentsForTicket(ticketId) {
  const allComments = [];
  let nextPage = `${ZENDESK_API_URL}/tickets/${ticketId}/comments`;

  // Create Basic Auth header
  const auth = Buffer.from(`${ZENDESK_USERNAME}:${ZENDESK_TOKEN}`).toString('base64');

  while (nextPage) {
    try {
      const response = await fetch(nextPage, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          console.log(`Ticket ${ticketId} not found, skipping...`);
          return [];
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Add comments to our array
      if (data.comments && Array.isArray(data.comments)) {
        allComments.push(...data.comments);
      }

      // Check for next page
      nextPage = data.next_page || null;

    } catch (error) {
      console.error(`Error fetching comments for ticket ${ticketId}:`, error);
      break;
    }
  }

  return allComments;
}

async function getAllComments() {
  // Read tickets from tickets.json
  let tickets;
  try {
    const ticketsData = fs.readFileSync('tickets.json', 'utf8');
    tickets = JSON.parse(ticketsData);
  } catch (error) {
    console.error('Error reading tickets.json:', error);
    return;
  }

  console.log(`Found ${tickets.length} tickets to process...`);

  const allComments = {};
  let processedCount = 0;

  for (const ticket of tickets) {
    const ticketId = ticket.id;
    console.log(`Processing ticket ${ticketId} (${processedCount + 1}/${tickets.length})...`);

    const comments = await getCommentsForTicket(ticketId);

    if (comments.length > 0) {
      allComments[ticketId] = comments;
      console.log(`  Found ${comments.length} comments for ticket ${ticketId}`);
    } else {
      console.log(`  No comments found for ticket ${ticketId}`);
    }

    processedCount++;

    // Add a small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return allComments;
}

async function main() {
  console.log('Starting to fetch all comments for all tickets...');

  const comments = await getAllComments();

  if (comments) {
    const totalTicketsWithComments = Object.keys(comments).length;
    const totalComments = Object.values(comments).reduce((sum, ticketComments) => sum + ticketComments.length, 0);

    console.log(`\nTotal tickets with comments: ${totalTicketsWithComments}`);
    console.log(`Total comments fetched: ${totalComments}`);

    // Save to JSON file
    try {
      fs.writeFileSync('comments.json', JSON.stringify(comments, null, 2));
      console.log('Comments saved to comments.json');
    } catch (error) {
      console.error('Error saving comments to file:', error);
    }
  }
}

// Run the script
main().catch(console.error);