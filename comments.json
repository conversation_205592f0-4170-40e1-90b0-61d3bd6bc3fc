{"1527": [{"id": 35081134314651, "type": "Comment", "author_id": 14152313892507, "body": "Do you have potatoes?\nthanks", "html_body": "<div class=\"zd-comment zd-comment-pre-styled\" dir=\"auto\"><div dir=\"ltr\">Do you have potatoes?<div>thanks</div></div></div>", "plain_body": "Do you have potatoes? thanks", "public": true, "attachments": [], "audit_id": 35081134314523, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-03-18T18:51:55Z", "metadata": {"system": {"message_id": "<<EMAIL>>", "email_id": "01JPN9J5RKNM5Z1R4CSEPDGBYR", "raw_email_identifier": "16904849/2d766567-678a-49c2-a869-0d364cfa3271.eml", "json_email_identifier": "16904849/2d766567-678a-49c2-a869-0d364cfa3271.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}], "1528": [{"id": 35082283258011, "type": "Comment", "author_id": 14152313892507, "body": "potatoes are pretty good", "html_body": "<div class=\"zd-comment zd-comment-pre-styled\" dir=\"auto\"><div dir=\"ltr\">potatoes are pretty good</div></div>", "plain_body": "potatoes are pretty good", "public": true, "attachments": [], "audit_id": 35082283257883, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-03-18T19:14:34Z", "metadata": {"system": {"message_id": "<CA+PA70doETnsyq9PSehmmJR4332vsBcoxMTC-2JTaBbnHcz=<EMAIL>>", "email_id": "01JPNAVM9AZ1V7YR0S5Y7XGEWF", "raw_email_identifier": "16904849/2dfdae1d-9a0e-4f1e-b829-377a9c409077.eml", "json_email_identifier": "16904849/2dfdae1d-9a0e-4f1e-b829-377a9c409077.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}], "1529": [{"id": 35082830692763, "type": "Comment", "author_id": 14152313892507, "body": "potatoes are the best", "html_body": "<div class=\"zd-comment zd-comment-pre-styled\" dir=\"auto\"><div dir=\"ltr\">potatoes are the best</div></div>", "plain_body": "potatoes are the best", "public": true, "attachments": [], "audit_id": 35082830692635, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-03-18T19:28:06Z", "metadata": {"system": {"message_id": "<<EMAIL>>", "email_id": "01JPNBME2WB911EGHJ66E39N8C", "raw_email_identifier": "16904849/8516c11d-2ffd-4a86-b18d-c253addcc3af.eml", "json_email_identifier": "16904849/8516c11d-2ffd-4a86-b18d-c253addcc3af.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}], "1530": [{"id": 35082832838171, "type": "Comment", "author_id": 14152313892507, "body": "hi lattes?", "html_body": "<div class=\"zd-comment zd-comment-pre-styled\" dir=\"auto\"><div dir=\"ltr\">hi lattes?</div></div>", "plain_body": "hi lattes?", "public": true, "attachments": [], "audit_id": 35082832838043, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-03-18T19:28:25Z", "metadata": {"system": {"message_id": "<<EMAIL>>", "email_id": "01JPNBN0R0J3BXE8XJGPTM5E85", "raw_email_identifier": "16904849/bc14acf1-ae20-456f-ad6b-2dbe322c9a80.eml", "json_email_identifier": "16904849/bc14acf1-ae20-456f-ad6b-2dbe322c9a80.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}], "1531": [{"id": 35082985625755, "type": "Comment", "author_id": 14152313892507, "body": "this is an email", "html_body": "<div class=\"zd-comment zd-comment-pre-styled\" dir=\"auto\"><div dir=\"ltr\">this is an email</div></div>", "plain_body": "this is an email", "public": true, "attachments": [], "audit_id": 35082985625627, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-03-18T19:31:50Z", "metadata": {"system": {"message_id": "<<EMAIL>>", "email_id": "01JPNBV8R7D2XE5DYAPD6V0ACA", "raw_email_identifier": "16904849/6dd7bffd-560d-4999-96a9-a35fdeea4dbf.eml", "json_email_identifier": "16904849/6dd7bffd-560d-4999-96a9-a35fdeea4dbf.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}], "1532": [{"id": 35083298909211, "type": "Comment", "author_id": 14152313892507, "body": "[No content]", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">[No content]</p></div>", "plain_body": "[No content]", "public": true, "attachments": [], "audit_id": 35083298909083, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-03-18T19:38:38Z", "metadata": {"system": {"message_id": "<CA+PA70f3z2hjrEafzaaaMmQnHhy2j=<EMAIL>>", "email_id": "01JPNC7PVSPHE1TK6FJCPVZWCY", "raw_email_identifier": "16904849/d76c2eb7-c0aa-4b70-bd21-e94c9e53f35c.eml", "json_email_identifier": "16904849/d76c2eb7-c0aa-4b70-bd21-e94c9e53f35c.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}], "1533": [{"id": 35083556659355, "type": "Comment", "author_id": 14152313892507, "body": "hi", "html_body": "<div class=\"zd-comment zd-comment-pre-styled\" dir=\"auto\"><div dir=\"ltr\">hi</div></div>", "plain_body": "hi", "public": true, "attachments": [], "audit_id": 35083556659227, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-03-18T19:46:08Z", "metadata": {"system": {"message_id": "<<EMAIL>>", "email_id": "01JPNCNEGZC1RJJG1XASVR1XYF", "raw_email_identifier": "16904849/efbf877f-c6e2-4069-a6e3-bb31ed947032.eml", "json_email_identifier": "16904849/efbf877f-c6e2-4069-a6e3-bb31ed947032.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}, {"id": 35084577992859, "type": "Comment", "author_id": 14152313892507, "body": "Requests #1536, #1535, #1534 were closed and merged into this request.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Requests <a href=\"/agent/tickets/1536\" target=\"_blank\" rel=\"ticket\">#1536</a>, <a href=\"/agent/tickets/1535\" target=\"_blank\" rel=\"ticket\">#1535</a>, <a href=\"/agent/tickets/1534\" target=\"_blank\" rel=\"ticket\">#1534</a> were closed and merged into this request.</p></div>", "plain_body": "Requests #1536, #1535, #1534 were closed and merged into this request.", "public": true, "attachments": [], "audit_id": 35084577992091, "via": {"channel": "system", "source": {"to": {}, "from": {"ticket_id": null, "subject": null, "ticket_ids": [1534, 1535, 1536]}, "rel": "merge"}}, "created_at": "2025-03-18T20:06:19Z", "metadata": {"system": {"location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "notifications_suppressed_for": [35083782139547, 35083847008667, 35083893678363]}}], "1534": [{"id": 35083759041947, "type": "Comment", "author_id": 35083782139547, "body": "Conversation with Web User 67d9ce8d6bfc481c51690cb8\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9ce8d6bfc481c51690cb8</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9ce8d6bfc481c51690cb8 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35083759041691, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T19:50:44Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35084234895259, "type": "Comment", "author_id": -1, "body": "(15:50:43) Web User 67d9ce8d6bfc481c51690cb8: test\n(15:50:49) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(15:50:43) Web User 67d9ce8d6bfc481c51690cb8: test\n<br>(15:50:49) Customer Service: Hello, World.</p></div>", "plain_body": "(15:50:43) Web User 67d9ce8d6bfc481c51690cb8: test\n\n(15:50:49) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35084234895003, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:00:43Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35084600666011, "type": "Comment", "author_id": 14152313892507, "body": "This request was closed and merged into request #1533 \"so many tickets\".", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">This request was closed and merged into request <a href=\"/agent/tickets/1533\" target=\"_blank\" rel=\"ticket\">#1533</a> \"so many tickets\".</p></div>", "plain_body": "This request was closed and merged into request #1533 \"so many tickets\".", "public": true, "attachments": [], "audit_id": 35084600665883, "via": {"channel": "system", "source": {"to": {}, "from": {"ticket_id": 1533, "subject": "so many tickets"}, "rel": "merge"}}, "created_at": "2025-03-18T20:06:17Z", "metadata": {"system": {"location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}}}], "1535": [{"id": 35083839920411, "type": "Comment", "author_id": 35083847008667, "body": "Conversation with Web User 67d9cecf86b03c3341b07741\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9cecf86b03c3341b07741</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9cecf86b03c3341b07741 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35083839920155, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T19:51:50Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35084323256091, "type": "Comment", "author_id": -1, "body": "(15:51:49) Web User 67d9cecf86b03c3341b07741: potatoes!\n(15:51:55) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(15:51:49) Web User 67d9cecf86b03c3341b07741: potatoes!\n<br>(15:51:55) Customer Service: Hello, World.</p></div>", "plain_body": "(15:51:49) Web User 67d9cecf86b03c3341b07741: potatoes!\n\n(15:51:55) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35084323255835, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:01:49Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35084600746395, "type": "Comment", "author_id": 14152313892507, "body": "This request was closed and merged into request #1533 \"so many tickets\".", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">This request was closed and merged into request <a href=\"/agent/tickets/1533\" target=\"_blank\" rel=\"ticket\">#1533</a> \"so many tickets\".</p></div>", "plain_body": "This request was closed and merged into request #1533 \"so many tickets\".", "public": true, "attachments": [], "audit_id": 35084600746267, "via": {"channel": "system", "source": {"to": {}, "from": {"ticket_id": 1533, "subject": "so many tickets"}, "rel": "merge"}}, "created_at": "2025-03-18T20:06:18Z", "metadata": {"system": {"location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}}}], "1536": [{"id": 35083893715867, "type": "Comment", "author_id": 35083893678363, "body": "Conversation with Web User 67d9cee9dd5890665655fe25\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9cee9dd5890665655fe25</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9cee9dd5890665655fe25 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35083893715611, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T19:52:15Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35084365814555, "type": "Comment", "author_id": -1, "body": "(15:52:14) Web User 67d9cee9dd5890665655fe25: lettuce talk to you\n(15:52:20) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(15:52:14) Web User 67d9cee9dd5890665655fe25: lettuce talk to you\n<br>(15:52:20) Customer Service: Hello, World.</p></div>", "plain_body": "(15:52:14) Web User 67d9cee9dd5890665655fe25: lettuce talk to you\n\n(15:52:20) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35084365814299, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:02:15Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35084577914139, "type": "Comment", "author_id": 14152313892507, "body": "This request was closed and merged into request #1533 \"so many tickets\".", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">This request was closed and merged into request <a href=\"/agent/tickets/1533\" target=\"_blank\" rel=\"ticket\">#1533</a> \"so many tickets\".</p></div>", "plain_body": "This request was closed and merged into request #1533 \"so many tickets\".", "public": true, "attachments": [], "audit_id": 35084577914011, "via": {"channel": "system", "source": {"to": {}, "from": {"ticket_id": 1533, "subject": "so many tickets"}, "rel": "merge"}}, "created_at": "2025-03-18T20:06:18Z", "metadata": {"system": {"location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}}}], "1537": [{"id": 35084009891995, "type": "Comment", "author_id": 35084009861019, "body": "Conversation with Web User 67d9cfab755e326b784ed8ec\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9cfab755e326b784ed8ec</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9cfab755e326b784ed8ec \n\n URL: None", "public": false, "attachments": [], "audit_id": 35084009891739, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T19:55:29Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35084567808155, "type": "Comment", "author_id": -1, "body": "(15:55:28) Web User 67d9cfab755e326b784ed8ec: test the potato\n(15:55:34) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(15:55:28) Web User 67d9cfab755e326b784ed8ec: test the potato\n<br>(15:55:34) Customer Service: Hello, World.</p></div>", "plain_body": "(15:55:28) Web User 67d9cfab755e326b784ed8ec: test the potato\n\n(15:55:34) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35084567807899, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:05:29Z", "metadata": {"system": {}, "custom": {}}}], "1538": [{"id": 35084781534747, "type": "Comment", "author_id": 35084804333723, "body": "Conversation with Web User 67d9d342188e521e15187930\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9d342188e521e15187930</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9d342188e521e15187930 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35084781534491, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:10:47Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35085252564635, "type": "Comment", "author_id": -1, "body": "(16:10:47) Web User 67d9d342188e521e15187930: hello\n(16:10:53) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:10:47) Web User 67d9d342188e521e15187930: hello\n<br>(16:10:53) Customer Service: Hello, World.</p></div>", "plain_body": "(16:10:47) Web User 67d9d342188e521e15187930: hello\n\n(16:10:53) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35085252564379, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:20:47Z", "metadata": {"system": {}, "custom": {}}}], "1539": [{"id": 35085690315163, "type": "Comment", "author_id": 29269182017051, "body": "Test text messaging", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Test text messaging</p></div>", "plain_body": "Test text messaging", "public": true, "attachments": [], "audit_id": 35085690315035, "via": {"channel": "sms", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:31:09Z", "metadata": {"system": {"client": "", "ip_address": ""}, "custom": {}}}, {"id": 35085783317403, "type": "Comment", "author_id": 35083081737243, "body": "response", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">response</p></div>", "plain_body": "response", "public": true, "attachments": [], "audit_id": 35085807644699, "via": {"channel": "web", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:33:25Z", "metadata": {"system": {"client": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ip_address": "*************", "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}}}], "1540": [{"id": 35085742771099, "type": "Comment", "author_id": 35085733542683, "body": "Conversation with Web User 67d9d81d1929d983518f780b\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9d81d1929d983518f780b</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9d81d1929d983518f780b \n\n URL: None", "public": false, "attachments": [], "audit_id": 35085742770843, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:32:27Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35086074529563, "type": "Comment", "author_id": -1, "body": "(16:32:27) Web User 67d9d81d1929d983518f780b: testing\n(16:32:33) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:32:27) Web User 67d9d81d1929d983518f780b: testing\n<br>(16:32:33) Customer Service: Hello, World.</p></div>", "plain_body": "(16:32:27) Web User 67d9d81d1929d983518f780b: testing\n\n(16:32:33) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35086074529307, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:39:36Z", "metadata": {"system": {}, "custom": {}}}], "1541": [{"id": 35085984357915, "type": "Comment", "author_id": 35085938216219, "body": "Conversation with Web User 67d9d95d2d3b80ed25e50756\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9d95d2d3b80ed25e50756</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9d95d2d3b80ed25e50756 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35085984357659, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:37:02Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35086200448539, "type": "Comment", "author_id": -1, "body": "(16:37:02) Web User 67d9d95d2d3b80ed25e50756: pumpkin convo\n(16:37:09) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:37:02) Web User 67d9d95d2d3b80ed25e50756: pumpkin convo\n<br>(16:37:09) Customer Service: Hello, World.</p></div>", "plain_body": "(16:37:02) Web User 67d9d95d2d3b80ed25e50756: pumpkin convo\n\n(16:37:09) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35086200448283, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:41:26Z", "metadata": {"system": {}, "custom": {}}}], "1542": [{"id": 35085976077339, "type": "Comment", "author_id": 35085976046491, "body": "Conversation with Web User 67d9d9841dcfcd53673936f1\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9d9841dcfcd53673936f1</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9d9841dcfcd53673936f1 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35085976077083, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:37:39Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35086314648219, "type": "Comment", "author_id": -1, "body": "(16:37:39) Web User 67d9d9841dcfcd53673936f1: potato convo\n(16:37:45) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:37:39) Web User 67d9d9841dcfcd53673936f1: potato convo\n<br>(16:37:45) Customer Service: Hello, World.</p></div>", "plain_body": "(16:37:39) Web User 67d9d9841dcfcd53673936f1: potato convo\n\n(16:37:45) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35086314647963, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:45:27Z", "metadata": {"system": {}, "custom": {}}}], "1543": [{"id": 35086034719387, "type": "Comment", "author_id": 35086034683547, "body": "Conversation with Web User 67d9d9c530761f1977835ac7\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9d9c530761f1977835ac7</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9d9c530761f1977835ac7 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35086034719131, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:38:42Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35086222703771, "type": "Comment", "author_id": -1, "body": "(16:38:41) Web User 67d9d9c530761f1977835ac7: pumpkin2 convo\n(16:38:47) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:38:41) Web User 67d9d9c530761f1977835ac7: pumpkin2 convo\n<br>(16:38:47) Customer Service: Hello, World.</p></div>", "plain_body": "(16:38:41) Web User 67d9d9c530761f1977835ac7: pumpkin2 convo\n\n(16:38:47) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35086222703515, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:43:29Z", "metadata": {"system": {}, "custom": {}}}], "1544": [{"id": 35086235159579, "type": "Comment", "author_id": 35086230841499, "body": "Conversation with Web User 67d9dad8c9e8027912acfcd9\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9dad8c9e8027912acfcd9</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9dad8c9e8027912acfcd9 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35086235159323, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:43:22Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35086267080347, "type": "Comment", "author_id": -1, "body": "(16:43:21) Web User 67d9dad8c9e8027912acfcd9: ok another potato is it recency?\n(16:43:27) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:43:21) Web User 67d9dad8c9e8027912acfcd9: ok another potato is it recency?\n<br>(16:43:27) Customer Service: Hello, World.</p></div>", "plain_body": "(16:43:21) Web User 67d9dad8c9e8027912acfcd9: ok another potato is it recency?\n\n(16:43:27) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35086267080091, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:44:54Z", "metadata": {"system": {}, "custom": {}}}], "1545": [{"id": 35086372371995, "type": "Comment", "author_id": 35086388627739, "body": "Conversation with Web User 67d9dba29f3a9eaa22551242\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9dba29f3a9eaa22551242</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9dba29f3a9eaa22551242 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35086372371739, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:46:32Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35086453095835, "type": "Comment", "author_id": -1, "body": "(16:46:32) Web User 67d9dba29f3a9eaa22551242: is potato\n(16:46:38) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:46:32) Web User 67d9dba29f3a9eaa22551242: is potato\n<br>(16:46:38) Customer Service: Hello, World.</p></div>", "plain_body": "(16:46:32) Web User 67d9dba29f3a9eaa22551242: is potato\n\n(16:46:38) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35086453095579, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:48:32Z", "metadata": {"system": {}, "custom": {}}}], "1546": [{"id": 35086419485723, "type": "Comment", "author_id": 35086419466011, "body": "Conversation with Web User 67d9dbb2bf42327c5fa99d5d\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9dbb2bf42327c5fa99d5d</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9dbb2bf42327c5fa99d5d \n\n URL: None", "public": false, "attachments": [], "audit_id": 35086419485467, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:46:48Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35086516878491, "type": "Comment", "author_id": -1, "body": "(16:46:47) Web User 67d9dbb2bf42327c5fa99d5d: is pumpkin\n(16:46:53) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:46:47) Web User 67d9dbb2bf42327c5fa99d5d: is pumpkin\n<br>(16:46:53) Customer Service: Hello, World.</p></div>", "plain_body": "(16:46:47) Web User 67d9dbb2bf42327c5fa99d5d: is pumpkin\n\n(16:46:53) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35086516878235, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:51:09Z", "metadata": {"system": {}, "custom": {}}}], "1547": [{"id": 35086433619483, "type": "Comment", "author_id": 35086433572507, "body": "Conversation with Web User 67d9dc2e65371be56420b6e1\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9dc2e65371be56420b6e1</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9dc2e65371be56420b6e1 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35086433619227, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:48:51Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35086628230683, "type": "Comment", "author_id": -1, "body": "(16:48:50) Web User 67d9dc2e65371be56420b6e1: another potato\n(16:48:56) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:48:50) Web User 67d9dc2e65371be56420b6e1: another potato\n<br>(16:48:56) Customer Service: Hello, World.</p></div>", "plain_body": "(16:48:50) Web User 67d9dc2e65371be56420b6e1: another potato\n\n(16:48:56) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35086628230427, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:53:07Z", "metadata": {"system": {}, "custom": {}}}], "1550": [{"id": 35086616533787, "type": "Comment", "author_id": 35086616503963, "body": "Conversation with Web User 67d9dd4634d76112732dc62a\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9dd4634d76112732dc62a</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9dd4634d76112732dc62a \n\n URL: None", "public": false, "attachments": [], "audit_id": 35086616533531, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T20:53:31Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35087142713499, "type": "Comment", "author_id": -1, "body": "(16:53:31) Web User 67d9dd4634d76112732dc62a: POTATO\n(16:53:37) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:53:31) Web User 67d9dd4634d76112732dc62a: POTATO\n<br>(16:53:37) Customer Service: Hello, World.</p></div>", "plain_body": "(16:53:31) Web User 67d9dd4634d76112732dc62a: POTATO\n\n(16:53:37) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35087142713243, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T21:03:31Z", "metadata": {"system": {}, "custom": {}}}], "1552": [{"id": 35087537879835, "type": "Comment", "author_id": 35087537850907, "body": "Conversation with Web User 67d9e1db7f1e2572c9daaeb8\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9e1db7f1e2572c9daaeb8</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9e1db7f1e2572c9daaeb8 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35087537879579, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T21:13:24Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35087870502555, "type": "Comment", "author_id": -1, "body": "(17:13:24) Web User 67d9e1db7f1e2572c9daaeb8: pumpkin1\n(17:13:30) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(17:13:24) Web User 67d9e1db7f1e2572c9daaeb8: pumpkin1\n<br>(17:13:30) Customer Service: Hello, World.</p></div>", "plain_body": "(17:13:24) Web User 67d9e1db7f1e2572c9daaeb8: pumpkin1\n\n(17:13:30) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35087870502299, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T21:23:24Z", "metadata": {"system": {}, "custom": {}}}], "1553": [{"id": 35087539179291, "type": "Comment", "author_id": 35087539148571, "body": "Conversation with Web User 67d9e1fd65371be5642e9f76\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9e1fd65371be5642e9f76</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9e1fd65371be5642e9f76 \n\n URL: None", "public": false, "attachments": [], "audit_id": 35087539179035, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T21:13:38Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35087911729051, "type": "Comment", "author_id": -1, "body": "(17:13:38) Web User 67d9e1fd65371be5642e9f76: potato1\n(17:13:44) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(17:13:38) Web User 67d9e1fd65371be5642e9f76: potato1\n<br>(17:13:44) Customer Service: Hello, World.</p></div>", "plain_body": "(17:13:38) Web User 67d9e1fd65371be5642e9f76: potato1\n\n(17:13:44) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35087911728795, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T21:23:38Z", "metadata": {"system": {}, "custom": {}}}], "1554": [{"id": 35087508782747, "type": "Comment", "author_id": 35087508762651, "body": "Conversation with Web User 67d9e20b9919fae1e5a6db7e\n\nURL: None", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Web User 67d9e20b9919fae1e5a6db7e</p>\n\n<p dir=\"auto\">URL: None</p></div>", "plain_body": "Conversation with Web User 67d9e20b9919fae1e5a6db7e \n\n URL: None", "public": false, "attachments": [], "audit_id": 35087508782491, "via": {"channel": "native_messaging", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T21:13:53Z", "metadata": {"system": {}, "custom": {}}}, {"id": 35087868279451, "type": "Comment", "author_id": -1, "body": "(17:13:53) Web User 67d9e20b9919fae1e5a6db7e: pumpkin2\n(17:13:59) Customer Service: Hello, World.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(17:13:53) Web User 67d9e20b9919fae1e5a6db7e: pumpkin2\n<br>(17:13:59) Customer Service: Hello, World.</p></div>", "plain_body": "(17:13:53) Web User 67d9e20b9919fae1e5a6db7e: pumpkin2\n\n(17:13:59) Customer Service: Hello, World.", "public": true, "attachments": [], "audit_id": 35087868279195, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-03-18T21:23:53Z", "metadata": {"system": {}, "custom": {}}}], "1565": [{"id": 35892487446683, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35892487446555, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:22:58Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1566": [{"id": 35892601586075, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35892601585947, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:25:00Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1567": [{"id": 35892682891803, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35892682891675, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:27:01Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1568": [{"id": 35892749470363, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35892749470235, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:29:03Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1569": [{"id": 35892876118299, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35892876118171, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:31:06Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1570": [{"id": 35892894784795, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35892894784667, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:33:08Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1571": [{"id": 35893006586139, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35893006586011, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:35:10Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1572": [{"id": 35893054875419, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35893054875291, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:37:12Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1573": [{"id": 35893203075611, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35893203075483, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:39:14Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1574": [{"id": 35893286141851, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35893286141723, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:41:16Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1575": [{"id": 35893385730075, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35893385729947, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:43:17Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1576": [{"id": 35893452629019, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35893452628891, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:45:19Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1577": [{"id": 35893587268123, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35893587267995, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:47:21Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1578": [{"id": 35893682804123, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35893682803995, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T14:50:13Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1579": [{"id": 35894181592091, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35894181591963, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:02:26Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1580": [{"id": 35894332747675, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35894332747547, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:04:28Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1581": [{"id": 35894463707675, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35894463707547, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:06:30Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1582": [{"id": 35894576428955, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35894576428827, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:08:32Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1583": [{"id": 35894689119131, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35894689119003, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:10:34Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1584": [{"id": 35894735987099, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35894735986971, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:12:36Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1585": [{"id": 35894817417243, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35894817417115, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:14:39Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1586": [{"id": 35894928533019, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35894928532891, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:16:41Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1587": [{"id": 35895025199899, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895025199771, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:18:43Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1588": [{"id": 35895105597723, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895105597595, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:20:45Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1589": [{"id": 35895168891035, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895168890907, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:22:47Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1590": [{"id": 35895204401563, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895204401435, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:24:50Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1591": [{"id": 35895289610523, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895289610395, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:26:52Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1592": [{"id": 35895410438427, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895410438299, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:28:54Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1593": [{"id": 35895486737819, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895486737691, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:30:56Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1594": [{"id": 35895584401051, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895584400923, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:32:58Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1595": [{"id": 35895621248411, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895621248283, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:35:00Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1596": [{"id": 35895733727003, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895733726875, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:37:02Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1597": [{"id": 35895829537947, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895829537819, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:39:04Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1598": [{"id": 35895895576731, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895895576603, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:41:06Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1599": [{"id": 35895974010651, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35895974010523, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:43:08Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1600": [{"id": 35896064209819, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896064209691, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:45:10Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1601": [{"id": 35896138237851, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896138237723, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:47:12Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1602": [{"id": 35896215933979, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896215933851, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:49:14Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1603": [{"id": 35896271066011, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896271065883, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:51:16Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1604": [{"id": 35896381647003, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896381646875, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:53:18Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1605": [{"id": 35896429749787, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896429749659, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:55:20Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1606": [{"id": 35896521368731, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896521368603, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:57:22Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1607": [{"id": 35896635369883, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896635369755, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T15:59:24Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1608": [{"id": 35896751707803, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896751707675, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:01:26Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1609": [{"id": 35896904185371, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35896904185243, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:03:28Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1610": [{"id": 35897006891547, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897006891419, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:05:31Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1611": [{"id": 35897140189851, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897140189723, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:07:33Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1612": [{"id": 35897263558683, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897263558555, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:09:35Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1613": [{"id": 35897311488795, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897311488667, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:11:37Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1614": [{"id": 35897443612571, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897443612443, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:13:39Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1615": [{"id": 35897528599067, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897528598939, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:15:41Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1616": [{"id": 35897596076827, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897596076699, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:17:43Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1617": [{"id": 35897703264283, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897703264155, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:19:46Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1618": [{"id": 35897819712411, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897819712283, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:21:48Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1619": [{"id": 35897941571227, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897941571099, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:23:50Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1620": [{"id": 35897995152667, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35897995152539, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:25:52Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1621": [{"id": 35898119659419, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898119659291, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:27:54Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1622": [{"id": 35898192673179, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898192673051, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:29:56Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1623": [{"id": 35898272260891, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898272260763, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:31:57Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1624": [{"id": 35898356131739, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898356131611, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:33:59Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1625": [{"id": 35898432199579, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898432199451, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:36:01Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1626": [{"id": 35898507347355, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898507347227, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:38:04Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1627": [{"id": 35898637158555, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898637158427, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:40:05Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1628": [{"id": 35898695180699, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898695180571, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:42:07Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1629": [{"id": 35898794231195, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898794231067, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:44:10Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1630": [{"id": 35898916186139, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898916186011, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:46:12Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1631": [{"id": 35898969483163, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35898969483035, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:48:14Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1632": [{"id": 35899134385179, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899134385051, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:50:16Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1633": [{"id": 35899173740827, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899173740699, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:52:18Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1634": [{"id": 35899256969883, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899256969755, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:54:20Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1635": [{"id": 35899331678235, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899331678107, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:56:22Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1636": [{"id": 35899462505499, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899462505371, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T16:58:24Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1637": [{"id": 35899561582107, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899561581979, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:00:26Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1638": [{"id": 35899693262235, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899693262107, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:02:28Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1639": [{"id": 35899830271515, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899830271387, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:04:30Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1640": [{"id": 35899926329243, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35899926329115, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:06:32Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1641": [{"id": 35900043180955, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900043180827, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:08:33Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1642": [{"id": 35900125977499, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900125977371, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:10:35Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1643": [{"id": 35900214356379, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900214356251, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:12:38Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1644": [{"id": 35900293812891, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900293812763, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:14:40Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1645": [{"id": 35900392214171, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900392214043, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:16:42Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1646": [{"id": 35900434460571, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900434460443, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:18:44Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1647": [{"id": 35900514837787, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900514837659, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:20:46Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1648": [{"id": 35900612682651, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900612682523, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:22:48Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1649": [{"id": 35900674213787, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900674213659, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:24:50Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1650": [{"id": 35900794360091, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900794359963, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:26:52Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1651": [{"id": 35900823990427, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900823990299, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:28:54Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1652": [{"id": 35900921195547, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35900921195419, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:30:56Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1653": [{"id": 35901046026395, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901046026267, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:32:58Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1654": [{"id": 35901107814939, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901107814811, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:35:00Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1655": [{"id": 35901176033307, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901176033179, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:37:02Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1656": [{"id": 35901287211675, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901287211547, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:39:04Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1657": [{"id": 35901393197211, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901393197083, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:41:06Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1658": [{"id": 35901524843163, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901524843035, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:43:07Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1659": [{"id": 35901594480283, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901594480155, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:45:10Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1660": [{"id": 35901706163611, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901706163483, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:47:12Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1661": [{"id": 35901796968603, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901796968475, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:49:14Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1662": [{"id": 35901930804379, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35901930804251, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:51:16Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1663": [{"id": 35902016026395, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35902016026267, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:53:18Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1664": [{"id": 35902124016795, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35902124016667, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:55:20Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1665": [{"id": 35902278957083, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35902278956955, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:57:22Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1666": [{"id": 35902407581851, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35902407581723, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T17:59:24Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1667": [{"id": 35902486850587, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35902486850459, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:01:26Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1668": [{"id": 35902694342043, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35902694341915, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:03:28Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1669": [{"id": 35902852909723, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35902852909595, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:05:30Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1670": [{"id": 35902958017307, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35902958017179, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:07:31Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1671": [{"id": 35903061005979, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903061005851, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:09:34Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1672": [{"id": 35903139100315, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903139100187, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:11:36Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1673": [{"id": 35903226795035, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903226794907, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:13:38Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1674": [{"id": 35903337246619, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903337246491, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:15:40Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1675": [{"id": 35903438021915, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903438021787, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:17:42Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1676": [{"id": 35903573827995, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903573827867, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:19:44Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1677": [{"id": 35903679861787, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903679861659, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:21:46Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1678": [{"id": 35903759479067, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903759478939, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:23:47Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1679": [{"id": 35903864377883, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903864377755, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:25:49Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1680": [{"id": 35903958545819, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35903958545691, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:27:52Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1681": [{"id": 35904056397211, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904056397083, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:29:54Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1682": [{"id": 35904105174043, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904105173915, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:31:56Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1683": [{"id": 35904194956443, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904194956315, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:33:57Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1684": [{"id": 35904305202075, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904305201947, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:36:00Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1685": [{"id": 35904412728219, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904412728091, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:38:01Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1686": [{"id": 35904491424283, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904491424155, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:40:04Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1687": [{"id": 35904628369179, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904628369051, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:42:06Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1688": [{"id": 35904696926875, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904696926747, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:44:08Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1689": [{"id": 35904798390299, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904798390171, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:46:10Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1690": [{"id": 35904911884315, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904911884187, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:48:12Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1691": [{"id": 35904940688027, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35904940687899, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:50:14Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1692": [{"id": 35905028952987, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905028952859, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:52:15Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1693": [{"id": 35905132662939, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905132662811, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:54:18Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1694": [{"id": 35905193430683, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905193430555, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:56:20Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1695": [{"id": 35905316295451, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905316295323, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T18:58:22Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1696": [{"id": 35905391764123, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905391763995, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:00:24Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1697": [{"id": 35905497978907, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905497978779, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:02:25Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1698": [{"id": 35905700993563, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905700993435, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:04:27Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1699": [{"id": 35905791319963, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905791319835, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:06:30Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "***********", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1700": [{"id": 35905923797147, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35905923797019, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:08:32Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "*************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1701": [{"id": 35906038156315, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35906038156187, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:10:34Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "*************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1702": [{"id": 35906085542939, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35906085542811, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:12:35Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "*************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1703": [{"id": 35906207202331, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35906207202203, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:14:38Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "*************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1704": [{"id": 35906331919131, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35906331919003, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:16:40Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "*************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1705": [{"id": 35906430381339, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35906430381211, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:18:42Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "*************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1706": [{"id": 35906575436955, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35906575436827, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:22:01Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "*************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1707": [{"id": 35906654309915, "type": "Comment", "author_id": 35890633182235, "body": "auto closed ticket", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">auto closed ticket</p></div>", "plain_body": "auto closed ticket", "public": false, "attachments": [], "audit_id": 35906654309787, "via": {"channel": "api", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-04-11T19:24:04Z", "metadata": {"system": {"client": "python-requests/2.32.3", "ip_address": "*************", "location": "Ashburn, VA, United States", "latitude": 39.0469, "longitude": -77.4903}, "custom": {}}}], "1708": [{"id": 36069688517787, "type": "Comment", "author_id": 14152313892507, "body": "Congratulations, you have set up Zendesk Talk and received your first call! Your new number is +****************. Click the Talk icon above to begin accepting calls.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Congratulations, you have set up Zendesk Talk and received your first call! Your new number is +****************. Click the Talk icon above to begin accepting calls.</p></div>", "plain_body": "Congratulations, you have set up Zendesk Talk and received your first call! Your new number is +****************. Click the Talk icon above to begin accepting calls.", "public": true, "attachments": [], "audit_id": 36069688517659, "via": {"channel": "api", "source": {"from": {}, "to": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, "rel": null}}, "created_at": "2025-04-17T00:24:53Z", "metadata": {"system": {"client": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ip_address": "*************", "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}}}], "1723": [{"id": 37693685988891, "type": "Comment", "author_id": 14152313892507, "body": "hi!", "html_body": "<div class=\"zd-comment zd-comment-pre-styled\" dir=\"auto\"><div dir=\"ltr\">hi!</div></div>", "plain_body": "hi!", "public": true, "attachments": [], "audit_id": 37693685988763, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-06-04T21:48:16Z", "metadata": {"system": {"message_id": "<<EMAIL>>", "email_id": "01JWYEN4MQSTK4G6G505E89R8V", "raw_email_identifier": "16904849/6e6a9fdf-483a-486a-9365-b46f79b1a445.eml", "json_email_identifier": "16904849/6e6a9fdf-483a-486a-9365-b46f79b1a445.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}, {"id": 37693717030555, "type": "Comment", "author_id": 14152313892507, "body": "hi back!", "html_body": "<div class=\"zd-comment\" dir=\"auto\">hi back!<br></div>", "plain_body": "hi back!", "public": true, "attachments": [], "audit_id": 37693743020187, "via": {"channel": "web", "source": {"from": {}, "to": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, "rel": null}}, "created_at": "2025-06-04T21:48:53Z", "metadata": {"system": {"client": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ip_address": "*************", "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}}}, {"id": 37693808520347, "type": "Comment", "author_id": 14152313892507, "body": "[No content]", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">[No content]</p></div>", "plain_body": "[No content]", "public": true, "attachments": [{"url": "https://d3v-anastasi-bakolias.zendesk.com/api/v2/attachments/37693782511259.json", "id": 37693782511259, "file_name": "catriminal.png", "content_url": "https://d3v-anastasi-bakolias.zendesk.com/attachments/token/0YUkB1kcr1yyNdAw08apTFdjs/?name=catriminal.png", "mapped_content_url": "https://d3v-anastasi-bakolias.zendesk.com/attachments/token/0YUkB1kcr1yyNdAw08apTFdjs/?name=catriminal.png", "content_type": "image/png", "size": 173489, "width": null, "height": null, "inline": false, "deleted": false, "malware_access_override": false, "malware_scan_result": "malware_not_found", "thumbnails": [{"url": "https://d3v-anastasi-bakolias.zendesk.com/api/v2/attachments/37693782660251.json", "id": 37693782660251, "file_name": "catriminal_thumb.png", "content_url": "https://d3v-anastasi-bakolias.zendesk.com/attachments/token/3hffAXbuQGmULYd50k60IVuaJ/?name=catriminal_thumb.png", "mapped_content_url": "https://d3v-anastasi-bakolias.zendesk.com/attachments/token/3hffAXbuQGmULYd50k60IVuaJ/?name=catriminal_thumb.png", "content_type": "image/png", "size": 11949, "width": 80, "height": 74, "inline": false, "deleted": false, "malware_access_override": false, "malware_scan_result": "not_scanned"}]}], "audit_id": 37693782510875, "via": {"channel": "email", "source": {"from": {"address": "s.bak<PERSON><EMAIL>", "name": "<PERSON><PERSON><PERSON>", "original_recipients": ["s.bak<PERSON><EMAIL>", "<EMAIL>"]}, "to": {"name": "Envoy", "address": "<EMAIL>"}, "rel": null}}, "created_at": "2025-06-04T21:50:54Z", "metadata": {"system": {"message_id": "<<EMAIL>>", "email_id": "01JWYESXNM76QJCCS7AR804BGZ", "raw_email_identifier": "16904849/23c53b61-cc04-4fe2-9b37-6cb3de4d0ec3.eml", "json_email_identifier": "16904849/23c53b61-cc04-4fe2-9b37-6cb3de4d0ec3.json", "eml_redacted": false, "location": "Sainte-Agathe-des-Monts, QC, Canada", "latitude": 46.0493, "longitude": -74.2922}, "custom": {}, "suspension_type_id": null}}], "1724": [{"id": 37816531298715, "type": "Comment", "author_id": 35745077766043, "body": "Conversation with <PERSON>", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with <PERSON></p></div>", "plain_body": "Conversation with <PERSON>", "public": false, "attachments": [], "audit_id": 37816531298459, "via": {"channel": "business_messaging_slack_connect", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-09T14:10:04Z", "metadata": {"system": {}, "custom": {}}}, {"id": 37817038034075, "type": "Comment", "author_id": -1, "body": "(10:10:03) <PERSON>: I have to resign from Zendesk today I think - managing the work and the expectations for attending meetings is increasing.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(10:10:03) <PERSON>: I have to resign from Zendesk today I think - managing the work and the expectations for attending meetings is increasing.</p></div>", "plain_body": "(10:10:03) <PERSON>: I have to resign from Zendesk today I think - managing the work and the expectations for attending meetings is increasing.", "public": true, "attachments": [], "audit_id": 37817038033819, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-09T14:20:04Z", "metadata": {"system": {}, "custom": {}}}, {"id": **************, "type": "Comment", "author_id": -1, "body": "(10:48:35) <PERSON>: Don't worry about it at all - even if you had, I would still have to show up for more meetings than I can accommodate with my new job.\nI really like having the extra paycheque, but I got 1.5 months of extra salary, and a new job that pays better, in a better environment.\nI'm a little sad to leave, but I'm walking away with all my debts paid off, a bunch of money in the bank, and a big set of transferrable skills - so thank you again for getting me this job.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(10:48:35) <PERSON>: Don't worry about it at all - even if you had, I would still have to show up for more meetings than I can accommodate with my new job.\n<br>I really like having the extra paycheque, but I got 1.5 months of extra salary, and a new job that pays better, in a better environment.\n<br>I'm a little sad to leave, but I'm walking away with all my debts paid off, a bunch of money in the bank, and a big set of transferrable skills - so thank you again for getting me this job.</p></div>", "plain_body": "(10:48:35) <PERSON>: Don't worry about it at all - even if you had, I would still have to show up for more meetings than I can accommodate with my new job.\n\nI really like having the extra paycheque, but I got 1.5 months of extra salary, and a new job that pays better, in a better environment.\n\nI'm a little sad to leave, but I'm walking away with all my debts paid off, a bunch of money in the bank, and a big set of transferrable skills - so thank you again for getting me this job.", "public": true, "attachments": [], "audit_id": **************, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-09T14:58:36Z", "metadata": {"system": {}, "custom": {}}}, {"id": **************, "type": "Comment", "author_id": -1, "body": "(12:34:29) <PERSON>: I have lunch break at 115\n(12:34:43) <PERSON>: For an hour I think\n(12:39:15) <PERSON>: Sure, or I can order lunch\n(12:45:21) <PERSON>: Can we order? I don't know if my lunch will get cut short.", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(12:34:29) <PERSON>: I have lunch break at 115\n<br>(12:34:43) <PERSON>: For an hour I think\n<br>(12:39:15) <PERSON>: Sure, or I can order lunch\n<br>(12:45:21) <PERSON>: Can we order? I don't know if my lunch will get cut short.</p></div>", "plain_body": "(12:34:29) <PERSON>: I have lunch break at 115\n\n(12:34:43) <PERSON>: For an hour I think\n\n(12:39:15) <PERSON>: Sure, or I can order lunch\n\n(12:45:21) <PERSON>: Can we order? I don't know if my lunch will get cut short.", "public": true, "attachments": [], "audit_id": 38043929626523, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-16T16:55:21Z", "metadata": {"system": {}, "custom": {}}}], "1725": [{"id": 38018075911579, "type": "Comment", "author_id": 36403257349403, "body": "Conversation with <PERSON><PERSON><PERSON>", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with Anastasi</p></div>", "plain_body": "Conversation with <PERSON><PERSON><PERSON>", "public": false, "attachments": [], "audit_id": 38018075911323, "via": {"channel": "business_messaging_slack_connect", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-15T19:10:40Z", "metadata": {"system": {}, "custom": {}}}, {"id": 38018201058331, "type": "Comment", "author_id": -1, "body": "(15:10:39) Ana<PERSON>si uploaded: Screenshot 2025-06-15 at 3.09.44 PM.png\nURL: https://d3v-anastasi-bakolias.zendesk.com/sc/attachments/v2/01JXTG0ETW4Z9GZGJJ6KK1JA9F/Screenshot%202025-06-15%20at%203.09.44%E2%80%AFPM.png\nType: image/png\nSize: 1042706", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(15:10:39) Anastasi uploaded: Screenshot 2025-06-15 at 3.09.44 PM.png\n<br>URL: <a href=\"https://d3v-anastasi-bakolias.zendesk.com/sc/attachments/v2/01JXTG0ETW4Z9GZGJJ6KK1JA9F/Screenshot%202025-06-15%20at%203.09.44%E2%80%AFPM.png\" target=\"_blank\" rel=\"nofollow noreferrer\">https://d3v-anastasi-bakolias.zendesk.com/sc/attachments/v2/01JXTG0ETW4Z9GZGJJ6KK1JA9F/Screenshot%202025-06-15%20at%203.09.44%E2%80%AFPM.png</a>\n<br>Type: image/png\n<br>Size: 1042706</p></div>", "plain_body": "(15:10:39) Ana<PERSON>si uploaded: Screenshot 2025-06-15 at 3.09.44 PM.png\n\nURL: https://d3v-anastasi-bakolias.zendesk.com/sc/attachments/v2/01JXTG0ETW4Z9GZGJJ6KK1JA9F/Screenshot%202025-06-15%20at%203.09.44%E2%80%AFPM.png\n\nType: image/png\n\nSize: 1042706", "public": true, "attachments": [], "audit_id": 38018201058075, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-15T19:20:39Z", "metadata": {"system": {}, "custom": {}}}], "1726": [{"id": 38091643013275, "type": "Comment", "author_id": 35188208462235, "body": "Conversation with <PERSON>", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with <PERSON></p></div>", "plain_body": "Conversation with <PERSON>", "public": false, "attachments": [], "audit_id": 38091643013019, "via": {"channel": "business_messaging_slack_connect", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-17T20:41:14Z", "metadata": {"system": {}, "custom": {}}}, {"id": 38092143827739, "type": "Comment", "author_id": -1, "body": "(16:41:14) <PERSON>: lp is so full of shit :joy:\nWe were running dagster in the cloud and someone mentioned in standup this morning that we should be running it locally and they would look into the setup, lp was like yes please this will help (I can also see him pushing to the feature branch and testing runs in the cloud NOT locally).\nI just got it working and pinged him to tell him how i did it and this is what he said:\n(16:41:40) <PERSON> uploaded: IMG_2281.png\nURL: https://d3v-anastasi-bakolias.zendesk.com/sc/attachments/v2/01JXZT0HEXX2Q4DJBCNE1A765E/IMG_2281.png\nType: image/png\nSize: 369141\n(16:43:55) <PERSON>: now he just deleted his feature branch with the run history :rolling_on_the_floor_laughing:", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(16:41:14) Nick: lp is so full of shit <img src=\"https://static.zdassets.com/classic/images/emojis/joy.png\" height=\"20px\" width=\"20px\" alt=\"joy\" title=\"joy\">\n<br>We were running dagster in the cloud and someone mentioned in standup this morning that we should be running it locally and they would look into the setup, lp was like yes please this will help (I can also see him pushing to the feature branch and testing runs in the cloud NOT locally).\n<br>I just got it working and pinged him to tell him how i did it and this is what he said:\n<br>(16:41:40) <PERSON> uploaded: IMG_2281.png\n<br>URL: <a href=\"https://d3v-anastasi-bakolias.zendesk.com/sc/attachments/v2/01JXZT0HEXX2Q4DJBCNE1A765E/IMG_2281.png\" target=\"_blank\" rel=\"nofollow noreferrer\">https://d3v-anastasi-bakolias.zendesk.com/sc/attachments/v2/01JXZT0HEXX2Q4DJBCNE1A765E/IMG_2281.png</a>\n<br>Type: image/png\n<br>Size: 369141\n<br>(16:43:55) Nick: now he just deleted his feature branch with the run history :rolling_on_the_floor_laughing:</p></div>", "plain_body": "(16:41:14) <PERSON>: lp is so full of shit \n\nWe were running dagster in the cloud and someone mentioned in standup this morning that we should be running it locally and they would look into the setup, lp was like yes please this will help (I can also see him pushing to the feature branch and testing runs in the cloud NOT locally).\n\nI just got it working and pinged him to tell him how i did it and this is what he said:\n\n(16:41:40) <PERSON> uploaded: IMG_2281.png\n\nURL: https://d3v-anastasi-bakolias.zendesk.com/sc/attachments/v2/01JXZT0HEXX2Q4DJBCNE1A765E/IMG_2281.png\n\nType: image/png\n\nSize: 369141\n\n(16:43:55) <PERSON>: now he just deleted his feature branch with the run history :rolling_on_the_floor_laughing:", "public": true, "attachments": [], "audit_id": 38092143827483, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-17T20:53:55Z", "metadata": {"system": {}, "custom": {}}}, {"id": 38094214869915, "type": "Comment", "author_id": -1, "body": "(17:41:26) <PERSON>: It doesn’t bother me, I just find it hilarious how insecure he is to not want to admit he was running it in the cloud and try to cover it up", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(17:41:26) <PERSON>: It doesn’t bother me, I just find it hilarious how insecure he is to not want to admit he was running it in the cloud and try to cover it up</p></div>", "plain_body": "(17:41:26) <PERSON>: It doesn’t bother me, I just find it hilarious how insecure he is to not want to admit he was running it in the cloud and try to cover it up", "public": true, "attachments": [], "audit_id": 38094214869147, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-17T21:51:26Z", "metadata": {"system": {}, "custom": {}}}], "1727": [{"id": 38238056421275, "type": "Comment", "author_id": 35313333043227, "body": "Conversation with Edyta Niemyjska", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">Conversation with <PERSON><PERSON><PERSON></p></div>", "plain_body": "Conversation with Edyta Niemyjska", "public": false, "attachments": [], "audit_id": 38238056421019, "via": {"channel": "business_messaging_slack_connect", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-22T23:07:23Z", "metadata": {"system": {}, "custom": {}}}, {"id": 38238182929435, "type": "Comment", "author_id": -1, "body": "(19:07:22) <PERSON><PERSON><PERSON>: 25-28 we are staying on the Farran campground.\n(19:07:25) <PERSON><PERSON><PERSON>: no boats\n(19:07:44) <PERSON><PERSON><PERSON>: do you want to offer your family to come and book themselves campspots there?\n(19:08:28) <PERSON><PERSON><PERSON>: Is <PERSON>s wedding starting at 7pm?", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(19:07:22) <PERSON><PERSON><PERSON>: 25-28 we are staying on the Farran campground.\n<br>(19:07:25) <PERSON><PERSON><PERSON>: no boats\n<br>(19:07:44) <PERSON><PERSON><PERSON>: do you want to offer your family to come and book themselves campspots there?\n<br>(19:08:28) <PERSON><PERSON><PERSON>: Is <PERSON>s wedding starting at 7pm?</p></div>", "plain_body": "(19:07:22) <PERSON><PERSON><PERSON>: 25-28 we are staying on the Farran campground.\n\n(19:07:25) <PERSON><PERSON><PERSON>: no boats\n\n(19:07:44) <PERSON><PERSON><PERSON>: do you want to offer your family to come and book themselves campspots there?\n\n(19:08:28) <PERSON><PERSON><PERSON>: Is <PERSON>s wedding starting at 7pm?", "public": true, "attachments": [], "audit_id": 38238182929179, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-22T23:18:29Z", "metadata": {"system": {}, "custom": {}}}, {"id": 38252524409243, "type": "Comment", "author_id": -1, "body": "(10:22:37) <PERSON><PERSON><PERSON>: garage sale this sunday?\n(10:22:57) <PERSON><PERSON><PERSON>: 6th or 29", "html_body": "<div class=\"zd-comment\" dir=\"auto\"><p dir=\"auto\">(10:22:37) <PERSON><PERSON><PERSON>: garage sale this sunday?\n<br>(10:22:57) <PERSON><PERSON><PERSON>: 6th or 29</p></div>", "plain_body": "(10:22:37) <PERSON><PERSON><PERSON>: garage sale this sunday?\n\n(10:22:57) <PERSON><PERSON><PERSON>: 6th or 29", "public": true, "attachments": [], "audit_id": 38252516422939, "via": {"channel": "chat_transcript", "source": {"from": {}, "to": {}, "rel": null}}, "created_at": "2025-06-23T14:32:57Z", "metadata": {"system": {}, "custom": {}}}]}