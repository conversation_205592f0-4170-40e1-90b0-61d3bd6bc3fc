/*
curl https://d3v-anastasi-bakolias.zendesk.com/api/v2/tickets/1723/comments.json -u '<EMAIL>/token:pilgY6Bm03S3wPcrvG91UeiXKEUfxCLUGzmse6fm' | json_pp
curl https://d3v-anastasi-bakolias.zendesk.com/api/v2/tickets/count -u '<EMAIL>/token:pilgY6Bm03S3wPcrvG91UeiXKEUfxCLUGzmse6fm' | json_pp
curl https://d3v-anastasi-bakolias.zendesk.com/api/v2/tickets -u '<EMAIL>/token:pilgY6Bm03S3wPcrvG91UeiXKEUfxCLUGzmse6fm' | json_pp
*/

import fs from 'fs';

const ZENDESK_USERNAME = '<EMAIL>/token';
const ZENDESK_TOKEN = 'pilgY6Bm03S3wPcrvG91UeiXKEUfxCLUGzmse6fm';
const ZENDESK_API_URL = 'https://d3v-anastasi-bakolias.zendesk.com/api/v2';

async function getAllTickets() {
  const allTickets = [];
  let nextPage = `${ZENDESK_API_URL}/tickets`;

  // Create Basic Auth header
  const auth = Buffer.from(`${ZENDESK_USERNAME}:${ZENDESK_TOKEN}`).toString('base64');

  while (nextPage) {
    console.log(`Fetching: ${nextPage}`);

    try {
      const response = await fetch(nextPage, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Add tickets to our array
      if (data.tickets && Array.isArray(data.tickets)) {
        allTickets.push(...data.tickets);
        console.log(`Fetched ${data.tickets.length} tickets. Total so far: ${allTickets.length}`);
      }

      // Check for next page
      nextPage = data.next_page || null;

    } catch (error) {
      console.error('Error fetching tickets:', error);
      break;
    }
  }

  return allTickets;
}

async function main() {
  console.log('Starting to fetch all tickets...');

  const tickets = await getAllTickets();

  console.log(`\nTotal tickets fetched: ${tickets.length}`);

  // Save to JSON file
  try {
    fs.writeFileSync('tickets.json', JSON.stringify(tickets, null, 2));
    console.log('Tickets saved to tickets.json');
  } catch (error) {
    console.error('Error saving tickets to file:', error);
  }
}

// Run the script
main().catch(console.error);

